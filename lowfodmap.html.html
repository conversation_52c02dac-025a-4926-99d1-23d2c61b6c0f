<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>G<PERSON><PERSON>: 10 Molhos Low-FODMAP Veganos</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --background: #5D240F;
            --foreground: #F7EFE5;
            --card: #5D240F;
            --card-foreground: #F7EFE5;
            --primary: #F7EFE5;
            --primary-foreground: #5D240F;
            --secondary: rgba(247, 239, 229, 0.1);
            --muted-foreground: rgba(247, 239, 229, 0.7);
            --border: rgba(247, 239, 229, 0.2);
            --ring: rgba(247, 239, 229, 0.3);
            --radius: 0.625rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background-color: var(--background);
            color: var(--foreground);
            font-family: 'Proxima Nova', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            scroll-behavior: smooth;
        }
        
        @font-face {
            font-family: 'Bodoni Moda';
            src: url('https://fonts.gstatic.com/s/bodonimoda/v9/aFT67PxzY382XsXX63LUYL6GYFcan6NJrKp-VPjfJMShrME.woff2') format('woff2');
            font-weight: 400;
            font-style: normal;
        }
        
        @font-face {
            font-family: 'Proxima Nova';
            src: url('https://fonts.gstatic.com/s/proximanova/v24/MQDlN6agP7zZEhWaX9_LL0ylq0b3r3t.woff2') format('woff2');
            font-weight: 400;
            font-style: normal;
        }
        
        .font-bodoni {
            font-family: 'Bodoni Moda', serif;
        }
        
        .hero-title {
            font-family: 'Bodoni Moda', serif;
            font-weight: 900;
            font-size: clamp(3rem, 8vw, 6rem);
            line-height: 1;
            letter-spacing: -1px;
        }
        
        .chapter-title {
            font-family: 'Bodoni Moda', serif;
            font-weight: 700;
            font-size: clamp(2rem, 5vw, 4rem);
            line-height: 1;
            letter-spacing: -1px;
        }
        
        .section-title {
            font-family: 'Bodoni Moda', serif;
            font-weight: 700;
            font-size: clamp(1.5rem, 4vw, 3rem);
            line-height: 1.1;
            letter-spacing: -1px;
        }
        
        .ingredient-title {
            font-family: 'Bodoni Moda', serif;
            font-weight: 700;
            font-size: 1.5rem;
            letter-spacing: 1px;
        }
        
        .body-text {
            font-family: 'Proxima Nova', sans-serif;
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        .quote-box {
            border-left: 4px solid #F7EFE5;
            padding-left: 1.25rem;
        }
        
        .chapter-letter {
            font-family: 'Bodoni Moda', serif;
            font-weight: 900;
            font-size: 12rem;
            opacity: 0.1;
            position: absolute;
            pointer-events: none;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }
        
        /* Navigation */
        nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 40;
            background: rgba(var(--background), 0.8);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border-bottom: 1px solid var(--border);
        }
        
        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
        }
        
        .logo {
            font-size: 1.25rem;
            font-weight: 700;
            letter-spacing: 0.5px;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
        }
        
        .nav-link {
            color: var(--muted-foreground);
            text-decoration: none;
            transition: color 0.3s ease;
            font-size: 1rem;
            position: relative;
            padding: 0.5rem 0;
        }
        
        .nav-link:hover, .nav-link.active {
            color: var(--primary);
        }
        
        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--primary);
        }
        
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--foreground);
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        /* Sections */
        section {
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }
        
        /* Hero Section */
        #home {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .hero-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(93, 36, 15, 0.9), rgba(93, 36, 15, 0.7), rgba(93, 36, 15, 0.3));
            z-index: -1;
        }
        
        .hero-bg-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -2;
        }
        
        .hero-content {
            text-align: center;
            max-width: 900px;
            padding: 2rem;
            position: relative;
            z-index: 10;
        }
        
        .hero-subtitle {
            font-size: 1.25rem;
            letter-spacing: 4px;
            text-transform: uppercase;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .hero-location {
            font-size: 1.25rem;
            letter-spacing: 4px;
            margin-top: 1rem;
            margin-bottom: 2rem;
        }
        
        .hero-description {
            letter-spacing: 2px;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .scroll-down {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-15px); }
            60% { transform: translateY(-7px); }
        }
        
        /* Introduction Section */
        #intro {
            display: flex;
            align-items: center;
            padding: 4rem 0;
        }
        
        .intro-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }
        
        .intro-image {
            border-radius: var(--radius);
            overflow: hidden;
            position: relative;
            height: 500px;
            background: #3a1508;
        }
        
        .intro-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        /* Molhos Section */
        .molhos-section {
            padding: 6rem 0;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 4rem;
            position: relative;
        }
        
        .molhos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .molho-card {
            background: var(--secondary);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .molho-card:hover {
            transform: translateY(-5px);
            border-color: rgba(247, 239, 229, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .molho-image {
            position: relative;
            height: 250px;
            overflow: hidden;
        }
        
        .molho-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .molho-card:hover .molho-image img {
            transform: scale(1.1);
        }
        
        .molho-number {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 4rem;
            font-weight: 700;
            opacity: 0.2;
        }
        
        .molho-info {
            padding: 1.5rem;
        }
        
        .molho-stats {
            color: var(--muted-foreground);
            font-size: 0.9rem;
        }
        
        /* Modal */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 100;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .modal.active {
            display: flex;
            opacity: 1;
        }
        
        .modal-content {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            max-width: 900px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            display: grid;
            grid-template-columns: 1fr;
        }
        
        @media (min-width: 768px) {
            .modal-content {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        .modal-image {
            position: relative;
            height: 400px;
        }
        
        .modal-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .modal-number {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 6rem;
            font-weight: 700;
            opacity: 0.2;
        }
        
        .modal-details {
            padding: 2rem;
        }
        
        .ingredient-list {
            margin: 1.5rem 0;
        }
        
        .ingredient-item {
            display: flex;
            margin-bottom: 0.5rem;
        }
        
        .ingredient-dot {
            width: 8px;
            height: 8px;
            background: var(--primary);
            border-radius: 50%;
            margin-top: 0.5rem;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }
        
        .close-modal {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            color: var(--foreground);
            font-size: 1.5rem;
            cursor: pointer;
            z-index: 10;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .mobile-menu-btn {
                display: block;
            }
            
            .intro-grid {
                grid-template-columns: 1fr;
            }
            
            .mobile-menu {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: var(--background);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 50;
                transform: translateY(-100%);
                transition: transform 0.3s ease;
            }
            
            .mobile-menu.active {
                transform: translateY(0);
            }
            
            .mobile-nav-links {
                display: flex;
                flex-direction: column;
                gap: 1.5rem;
                text-align: center;
            }
            
            .mobile-nav-link {
                color: var(--foreground);
                text-decoration: none;
                font-size: 1.25rem;
                padding: 0.5rem;
            }
            
            .close-mobile-menu {
                position: absolute;
                top: 1.5rem;
                right: 1.5rem;
                background: none;
                border: none;
                color: var(--foreground);
                font-size: 1.5rem;
                cursor: pointer;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav>
        <div class="nav-container">
            <div class="logo font-bodoni">MOLHOS LOW-FODMAP</div>
            <div class="nav-links">
                <a href="#home" class="nav-link active">Início</a>
                <a href="#intro" class="nav-link">Sobre</a>
                <a href="#essenciais" class="nav-link">Essenciais</a>
                <a href="#frescos" class="nav-link">Frescos</a>
                <a href="#classicos" class="nav-link">Clássicos</a>
            </div>
            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>
    
    <!-- Mobile Menu -->
    <div class="mobile-menu">
        <button class="close-mobile-menu">
            <i class="fas fa-times"></i>
        </button>
        <div class="mobile-nav-links">
            <a href="#home" class="mobile-nav-link">Início</a>
            <a href="#intro" class="mobile-nav-link">Sobre</a>
            <a href="#essenciais" class="mobile-nav-link">Essenciais</a>
            <a href="#frescos" class="mobile-nav-link">Frescos</a>
            <a href="#classicos" class="mobile-nav-link">Clássicos</a>
        </div>
    </div>
    
    <!-- Hero Section -->
    <section id="home">
        <div class="hero-bg"></div>
        <img src="hero-image.png" alt="Molhos" class="hero-bg-img">
        <div class="container">
            <div class="hero-content">
                <div class="hero-subtitle font-bodoni">GUIA COMPLETO</div>
                <h1 class="hero-title">10 MOLHOS LOW-FODMAP VEGANOS</h1>
                <div class="hero-location font-bodoni">(PARA PORTUGAL)</div>
                <p class="hero-description">FORMATO SIMPLIFICADO PARA USAR DIRETO NA COZINHA</p>
            </div>
        </div>
        <a href="#intro" class="scroll-down">
            <i class="fas fa-chevron-down"></i>
        </a>
        <div class="chapter-letter" style="right: 3rem; bottom: 3rem;">M</div>
    </section>
    
    <!-- Introduction Section -->
    <section id="intro">
        <div class="container">
            <div class="intro-grid">
                <div class="intro-image">
                    <img src="intro_image2.png" alt="Ingredientes para molhos">
                </div>
                <div>
                    <div class="font-bodoni" style="font-size: 1.25rem; letter-spacing: 4px; margin-bottom: 1rem; opacity: 0.8;">FORMATO SIMPLIFICADO</div>
                    <h2 class="chapter-title">PARA USAR DIRETO NA COZINHA</h2>
                    <div class="quote-box" style="margin: 2rem 0;">
                        <p class="body-text italic">
                            "Uma coleção de molhos veganos adaptados para dietas low-FODMAP, 
                            especialmente formulados para quem busca sabor sem comprometer o bem-estar digestivo."
                        </p>
                    </div>
                    <p class="body-text">
                        Os molhos low-FODMAP são preparados sem ingredientes fermentáveis que podem causar 
                        desconforto digestivo em pessoas sensíveis.
                    </p>
                    <p class="body-text" style="margin-top: 1rem;">
                        Cada receita foi cuidadosamente adaptada para o mercado português, 
                        utilizando ingredientes facilmente encontrados localmente.
                    </p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Molhos Essenciais -->
    <section id="essenciais" class="molhos-section">
        <div class="container">
            <div class="section-header">
                <div class="chapter-letter" style="left: 50%; top: 50%; transform: translate(-50%, -50%);">A</div>
                <h2 class="chapter-title">MOLHOS ESSENCIAIS</h2>
            </div>
            
            <div class="molhos-grid">
                <!-- Molho de Soja -->
                <div class="molho-card" data-molho="1">
                    <div class="molho-image">
                        <img src="molhosoja.jpg" alt="Molho de Soja Adaptado">
                        <div class="molho-number font-bodoni">1</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">MOLHO DE SOJA ADAPTADO</h3>
                        <p class="molho-stats">4 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
                
                <!-- Molho Teriyaki -->
                <div class="molho-card" data-molho="2">
                    <div class="molho-image">
                        <img src="teriyaki.png" alt="Molho Teriyaki">
                        <div class="molho-number font-bodoni">2</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">MOLHO TERIYAKI</h3>
                        <p class="molho-stats">5 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
                
                <!-- Molho de Amendoim -->
                <div class="molho-card" data-molho="3">
                    <div class="molho-image">
                        <img src="molhoamendoim.jpg" alt="Molho de Amendoim">
                        <div class="molho-number font-bodoni">3</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">MOLHO DE AMENDOIM</h3>
                        <p class="molho-stats">5 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Sabores Frescos -->
    <section id="frescos" class="molhos-section">
        <div class="container">
            <div class="section-header">
                <div class="chapter-letter" style="left: 50%; top: 50%; transform: translate(-50%, -50%);">B</div>
                <h2 class="chapter-title">SABORES FRESCOS</h2>
            </div>
            
            <div class="molhos-grid">
                <!-- Chimichurri Verde -->
                <div class="molho-card" data-molho="4">
                    <div class="molho-image">
                        <img src="chimmichurri.jpg" alt="Chimichurri Verde">
                        <div class="molho-number font-bodoni">4</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">CHIMICHURRI VERDE</h3>
                        <p class="molho-stats">5 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
                
                <!-- Tzatziki Vegano -->
                <div class="molho-card" data-molho="5">
                    <div class="molho-image">
                        <img src="tzatzikiveg.jpg" alt="Tzatziki Vegano">
                        <div class="molho-number font-bodoni">5</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">TZATZIKI VEGANO</h3>
                        <p class="molho-stats">5 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
                
                <!-- Pesto Sem Alho -->
                <div class="molho-card" data-molho="6">
                    <div class="molho-image">
                        <img src="pesto.jpeg" alt="Pesto Sem Alho">
                        <div class="molho-number font-bodoni">6</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">PESTO SEM ALHO</h3>
                        <p class="molho-stats">6 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Clássicos Veganos -->
    <section id="classicos" class="molhos-section">
        <div class="container">
            <div class="section-header">
                <div class="chapter-letter" style="left: 50%; top: 50%; transform: translate(-50%, -50%);">C</div>
                <h2 class="chapter-title">CLÁSSICOS VEGANOS</h2>
            </div>
            
            <div class="molhos-grid">
                <!-- Maionese Vegana -->
                <div class="molho-card" data-molho="7">
                    <div class="molho-image">
                        <img src="maionesevegana.jpg" alt="Maionese Vegana">
                        <div class="molho-number font-bodoni">7</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">MAIONESE VEGANA</h3>
                        <p class="molho-stats">4 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
                
                <!-- Molho BBQ -->
                <div class="molho-card" data-molho="8">
                    <div class="molho-image">
                        <img src="molhobbq.jpg" alt="Molho BBQ">
                        <div class="molho-number font-bodoni">8</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">MOLHO BBQ</h3>
                        <p class="molho-stats">4 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
                
                <!-- Molho de Hortelã -->
                <div class="molho-card" data-molho="9">
                    <div class="molho-image">
                        <img src="freshmintsauce.jpg" alt="Molho de Hortelã">
                        <div class="molho-number font-bodoni">9</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">MOLHO DE HORTELÃ</h3>
                        <p class="molho-stats">4 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
                
                <!-- Molho Agridoce -->
                <div class="molho-card" data-molho="10">
                    <div class="molho-image">
                        <img src="agridoce.jpg" alt="Molho Agridoce">
                        <div class="molho-number font-bodoni">10</div>
                    </div>
                    <div class="molho-info">
                        <h3 class="section-title">MOLHO AGRIDOCE</h3>
                        <p class="molho-stats">5 ingredientes • Clique para ver a receita</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Modal -->
    <div class="modal">
        <div class="modal-content">
            <button class="close-modal">
                <i class="fas fa-times"></i>
            </button>
            <div class="modal-image">
                <img id="modal-img" src="molhosoja.jpg" alt="Molho">
                <div id="modal-number" class="modal-number font-bodoni">1</div>
            </div>
            <div class="modal-details">
                <h2 id="modal-title" class="section-title">MOLHO DE SOJA ADAPTADO</h2>
                
                <div>
                    <h3 class="ingredient-title">INGREDIENTES:</h3>
                    <ul id="ingredient-list" class="ingredient-list">
                        <!-- Inserido via JavaScript -->
                    </ul>
                </div>
                
                <div>
                    <h3 class="ingredient-title">PREPARO:</h3>
                    <p id="preparation" class="body-text"></p>
                </div>
                
                <div id="quote-container" class="quote-box" style="margin-top: 2rem; display: none;">
                    <p id="quote-text" class="body-text italic"></p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Dados completos dos molhos
        const molhosData = {
            1: {
                title: "MOLHO DE SOJA ADAPTADO",
                image: "molhosoja.jpg",
                ingredients: [
                    "3 col. sopa de molho de soja Tamari (sem glúten/alho)",
                    "1 col. chá de óleo de gergelim",
                    "1 col. chá de vinagre de arroz",
                    "1 col. chá de xarope de ácer"
                ],
                preparation: "Misture todos os ingredientes em um recipiente. Guarde na geladeira em um frasco bem fechado. Dura aproximadamente 1 mês.",
                quote: "Perfeito para saladas, rolinhos primavera e pratos asiáticos."
            },
            2: {
                title: "MOLHO TERIYAKI",
                image: "teriyaki.png",
                ingredients: [
                    "4 col. sopa do molho de soja adaptado",
                    "2 col. sopa de xarope de ácer",
                    "1 col. sopa de vinagre de arroz",
                    "1 col. chá de gengibre ralado",
                    "1 col. chá de amido de milho + 2 col. água"
                ],
                preparation: "Misture todos os ingredientes em uma panela pequena. Leve ao fogo médio, mexendo constantemente até engrossar. Deixe esfriar antes de usar.",
                quote: ""
            },
            3: {
                title: "MOLHO DE AMENDOIM",
                image: "molhoamendoim.jpg",
                ingredients: [
                    "3 col. sopa de pasta de amendoim pura",
                    "1 col. sopa de molho de soja adaptado",
                    "1 col. sopa de sumo de limão",
                    "1 col. chá de gengibre ralado",
                    "Água (até ficar cremoso)"
                ],
                preparation: "Misture todos os ingredientes com um fouet até obter uma consistência cremosa. Adicione água aos poucos para ajustar a textura conforme necessário.",
                quote: "Perfeito para saladas, rolinhos primavera e pratos asiáticos."
            },
            4: {
                title: "CHIMICHURRI VERDE",
                image: "chimmichurri.jpg",
                ingredients: [
                    "½ xícara de salsinha picada",
                    "3 col. sopa de azeite",
                    "2 col. sopa de vinagre tinto",
                    "1 col. sopa de cebolinha (só parte verde)",
                    "Sal e pimenta a gosto"
                ],
                preparation: "Misture todos os ingredientes em uma tigela. Deixe descansar por 30 minutos antes de servir para que os sabores se desenvolvam. Conserve na geladeira por até 3 dias.",
                quote: "Acompanhamento perfeito para grelhados e assados."
            },
            5: {
                title: "TZATZIKI VEGANO",
                image: "tzatzikiveg.jpg",
                ingredients: [
                    "200g de tofu firme escorrido",
                    "½ pepino ralado e espremido",
                    "1 col. sopa de azeite",
                    "1 col. sopa de vinagre de maçã",
                    "1 col. chá de endro fresco"
                ],
                preparation: "Bata o tofu no processador até ficar cremoso. Misture o pepino ralado e espremido, o azeite, o vinagre e o endro. Tempere com sal a gosto e refrigere por pelo menos 1 hora antes de servir.",
                quote: "Versão vegana do clássico molho grego, perfeito para acompanhar pão pita e falafel."
            },
            6: {
                title: "PESTO SEM ALHO",
                image: "pesto.jpeg",
                ingredients: [
                    "2 xícaras de manjericão fresco",
                    "3 col. sopa de pinholis",
                    "4 col. sopa de azeite",
                    "1 col. sopa de sumo de limão",
                    "2 col. sopa de 'parmesão' vegano",
                    "1 col. chá de óleo de alho infusionado"
                ],
                preparation: "Bata tudo no processador até obter uma pasta homogênea. Ajuste o sal a gosto.",
                quote: ""
            },
            7: {
                title: "MAIONESE VEGANA",
                image: "maionesevegana.jpg",
                ingredients: [
                    "150ml de leite de soja sem açúcar",
                    "1 col. sopa de vinagre de maçã",
                    "1 col. chá de mostarda Dijon",
                    "200ml de óleo de girassol"
                ],
                preparation: "Bata o leite de soja, vinagre e mostarda. Adicione o óleo em fio fino enquanto continua batendo até emulsionar e obter consistência de maionese.",
                quote: ""
            },
            8: {
                title: "MOLHO BBQ",
                image: "molhobbq.jpg",
                ingredients: [
                    "4 col. sopa de polpa de tomate pura",
                    "2 col. sopa de xarope de ácer",
                    "1 col. sopa de vinagre de cidra",
                    "1 col. chá de páprica defumada"
                ],
                preparation: "Misture todos os ingredientes em uma panela pequena. Cozinhe em fogo baixo por 5 minutos, mexendo ocasionalmente. Deixe esfriar antes de usar.",
                quote: ""
            },
            9: {
                title: "MOLHO DE HORTELÃ",
                image: "freshmintsauce.jpg",
                ingredients: [
                    "¼ xícara de hortelã fresca picada",
                    "2 col. sopa de sumo de limão",
                    "3 col. sopa de azeite",
                    "1 col. chá de cominho em pó"
                ],
                preparation: "Misture todos os ingredientes em uma tigela pequena. Use fresco para melhor sabor. Ideal para acompanhar carnes grelhadas e pratos mediterrâneos.",
                quote: ""
            },
            10: {
                title: "MOLHO AGRIDOCE",
                image: "agridoce.jpg",
                ingredients: [
                    "3 col. sopa de molho de soja adaptado",
                    "2 col. sopa de vinagre de arroz",
                    "1 col. sopa de xarope de ácer",
                    "1 col. chá de gengibre ralado",
                    "1 col. chá de amido de milho + água"
                ],
                preparation: "Misture todos os ingredientes em uma panela pequena. Leve ao fogo médio, mexendo até engrossar. Perfeito para pratos asiáticos e rolinhos primavera.",
                quote: ""
            }
        };

        // Mobile Menu Toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const mobileMenu = document.querySelector('.mobile-menu');
        const closeMobileMenu = document.querySelector('.close-mobile-menu');
        
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.add('active');
        });
        
        closeMobileMenu.addEventListener('click', () => {
            mobileMenu.classList.remove('active');
        });
        
        // Close mobile menu when clicking a link
        document.querySelectorAll('.mobile-nav-link').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.remove('active');
            });
        });
        
        // Modal functionality
        const modal = document.querySelector('.modal');
        const closeModal = document.querySelector('.close-modal');
        const molhoCards = document.querySelectorAll('.molho-card');
        const modalImg = document.getElementById('modal-img');
        const modalNumber = document.getElementById('modal-number');
        const modalTitle = document.getElementById('modal-title');
        const ingredientList = document.getElementById('ingredient-list');
        const preparation = document.getElementById('preparation');
        const quoteContainer = document.getElementById('quote-container');
        const quoteText = document.getElementById('quote-text');
        
        molhoCards.forEach(card => {
            card.addEventListener('click', () => {
                const molhoId = card.getAttribute('data-molho');
                const molho = molhosData[molhoId];
                
                // Preencher o modal com os dados do molho
                modalImg.src = molho.image;
                modalNumber.textContent = molhoId;
                modalTitle.textContent = molho.title;
                
                // Limpar e preencher a lista de ingredientes
                ingredientList.innerHTML = '';
                molho.ingredients.forEach(ingredient => {
                    const li = document.createElement('li');
                    li.className = 'ingredient-item';
                    li.innerHTML = `
                        <span class="ingredient-dot"></span>
                        <span class="body-text">${ingredient}</span>
                    `;
                    ingredientList.appendChild(li);
                });
                
                // Preencher o modo de preparo
                preparation.textContent = molho.preparation;
                
                // Mostrar ou esconder a citação
                if (molho.quote) {
                    quoteText.textContent = molho.quote;
                    quoteContainer.style.display = 'block';
                } else {
                    quoteContainer.style.display = 'none';
                }
                
                // Mostrar o modal
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        });
        
        closeModal.addEventListener('click', () => {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        });
        
        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
        
        // Navigation scroll and active link
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.nav-link');
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
        
        window.addEventListener('scroll', () => {
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 100) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').substring(1) === current) {
                    link.classList.add('active');
                }
            });
            
            mobileNavLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').substring(1) === current) {
                    link.classList.add('active');
                }
            });
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    window.scrollTo({
                        top: target.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Card hover effect
        molhoCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px)';
                card.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.2)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
                card.style.boxShadow = '';
            });
        });
    </script>
</body>
</html>